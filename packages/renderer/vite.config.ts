import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@app/shared': resolve(__dirname, '../shared'),
      '@app/rve-shared': resolve(__dirname, '../rve-shared'),
      '@rve/editor': resolve(__dirname, 'src/modules/video-editor/components/editor/version-7.0.0'),
      '@rve/renderer': resolve(__dirname, 'src/modules/video-editor/components/renderer'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
  },
})
