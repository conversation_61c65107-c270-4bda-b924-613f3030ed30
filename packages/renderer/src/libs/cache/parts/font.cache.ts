import { SubCacheManager } from '../types'
import opentype from 'opentype.js'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

export class FontCacheManager extends SubCacheManager {

  private memoryCache: Map<string, opentype.Font> = new Map()

  public async cacheFont(src: string) {
    if (this.memoryCache.has(src)) return

    const localCachedUrl = await window.resource.getPath({
      url: src,
      type: ResourceType.FONT
    })

    const font = localCachedUrl
      ? await opentype.load(`file://${localCachedUrl}`)
      : await opentype.load(src)

    this.memoryCache.set(src, font)
    // console.debug(`[font.cache:14] 已将字体${font.names.fontFamily.zh || font.names.fontFamily.en}缓存到内存`)
  }

  public getFont(src: string) {
    return this.memoryCache.get(src) || null
  }

  cleanup(_now: number, _maxAge: number) {
    this.memoryCache.clear()
  }
}
