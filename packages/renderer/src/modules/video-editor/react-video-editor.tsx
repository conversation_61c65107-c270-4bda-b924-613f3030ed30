import React from 'react'

import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable.tsx'
import { Materials } from '@rve/editor/components/materials.tsx'
import { Editor } from '@rve/editor/components/core/editor.tsx'
import { HotReloadErrorBoundary } from '@rve/editor/components/hot-reload-error-boundary.tsx'

import {
  SidebarProvider as EditorSidebarProvider,
  EditorProvider,
  TimelineProvider,
  AssetLoadingProvider,
  CachedOverlaysProvider
} from './components/editor/version-7.0.0/contexts'

export default React.memo(
  function ReactVideoEditor({ scriptId, projectId }: { scriptId: string, projectId: string }) {
    return (
      <HotReloadErrorBoundary>
        <EditorSidebarProvider>
          <EditorProvider scriptId={scriptId} projectId={projectId}>
          <TimelineProvider>
            <CachedOverlaysProvider>
              <AssetLoadingProvider>
                <div className="flex flex-col size-full">
                  <div className="w-full">
                    <Editor.Header />
                  </div>

                  <div className="flex-1 w-full flex">
                    <Materials.Sidebar />

                    <ResizablePanelGroup direction="vertical" className="h-full flex-1">
                      <ResizablePanel defaultSize={50} minSize={50}>
                        <ResizablePanelGroup direction="horizontal">
                          <ResizablePanel defaultSize={24} minSize={15} maxSize={30}>
                            <Materials.Content />
                          </ResizablePanel>
                          <ResizableHandle />

                          <ResizablePanel defaultSize={60} minSize={40}>
                            <Editor.Player />
                          </ResizablePanel>
                          <ResizableHandle />

                          <ResizablePanel defaultSize={22} minSize={15} maxSize={30}>
                            <Editor.OverlayDetail />
                          </ResizablePanel>
                        </ResizablePanelGroup>
                      </ResizablePanel>
                      <ResizableHandle />

                      <ResizablePanel defaultSize={50} minSize={5}>
                        <div className="h-full overflow-y-hidden flex flex-col">
                          <Editor.Toolbar />
                          <Editor.Timeline />
                        </div>
                      </ResizablePanel>
                    </ResizablePanelGroup>
                  </div>
                </div>
              </AssetLoadingProvider>
            </CachedOverlaysProvider>
          </TimelineProvider>
        </EditorProvider>
      </EditorSidebarProvider>
      </HotReloadErrorBoundary>
    )
  }
)
