import React, { Component, ReactNode } from 'react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  isHotReloadError: boolean
}

/**
 * 错误边界组件，专门处理热重载时的上下文错误
 * 
 * 在开发环境中，热重载可能会导致 React Context 暂时不可用，
 * 这个组件会捕获这类错误并显示友好的加载状态，而不是让整个应用崩溃。
 */
export class HotReloadErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: NodeJS.Timeout | null = null

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      isHotReloadError: false
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // 检查是否是热重载导致的上下文错误
    const isHotReloadError = error.message === 'EDITOR_CONTEXT_HOT_RELOAD' ||
      error.message.includes('useEditorContext must be used within an EditorProvider')

    return {
      hasError: true,
      error,
      isHotReloadError
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (this.state.isHotReloadError) {
      console.warn('[HotReloadErrorBoundary] 捕获到热重载错误:', error.message)
      
      // 在开发环境中，自动重试渲染
      if (import.meta.env.DEV) {
        this.scheduleRetry()
      }
    } else {
      console.error('[HotReloadErrorBoundary] 捕获到未知错误:', error, errorInfo)
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  private scheduleRetry = () => {
    // 清除之前的重试计时器
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }

    // 在短暂延迟后重试渲染
    this.retryTimeoutId = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        isHotReloadError: false
      })
    }, 100) // 100ms 延迟，给 React 时间重新初始化上下文
  }

  private handleManualRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      isHotReloadError: false
    })
  }

  render() {
    if (this.state.hasError) {
      if (this.state.isHotReloadError && import.meta.env.DEV) {
        // 热重载错误的友好显示
        return (
          <div className="flex items-center justify-center h-full w-full bg-gray-50">
            <div className="text-center p-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <div className="text-sm text-gray-600 mb-2">编辑器正在重新初始化...</div>
              <div className="text-xs text-gray-500 mb-4">热重载导致的临时状态</div>
              <button
                onClick={this.handleManualRetry}
                className="px-4 py-2 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                手动重试
              </button>
            </div>
          </div>
        )
      } else {
        // 其他错误的显示
        return (
          <div className="flex items-center justify-center h-full w-full bg-red-50">
            <div className="text-center p-6">
              <div className="text-red-600 mb-2">编辑器加载失败</div>
              <div className="text-xs text-gray-600 mb-4">
                {this.state.error?.message || '未知错误'}
              </div>
              <button
                onClick={this.handleManualRetry}
                className="px-4 py-2 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
              >
                重新加载
              </button>
            </div>
          </div>
        )
      }
    }

    return this.props.children
  }
}
