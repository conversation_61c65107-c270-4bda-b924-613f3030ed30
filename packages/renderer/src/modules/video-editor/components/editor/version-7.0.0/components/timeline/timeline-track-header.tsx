import React, { FC, useCallback, useMemo, useState } from 'react'
import { useTimeline } from '@rve/editor/contexts/timeline/timeline.context.tsx'
import clsx from 'clsx'
import { Camera, Grip, Image, Layers, Mic, Music, Type, Video } from 'lucide-react'
import { Track, TrackType } from '@app/rve-shared/types'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'
import { ENABLE_TRACK_DRAG } from '@rve/editor/constants'
import { WithTooltip } from '@/components/WithTooltip.tsx'
import { getTrackTypeLabel } from '@rve/editor/utils/track-helper.ts'

type TimelineTrackHeaderProps = Pick<Track, 'type' | 'isGlobalTrack'> & {
  trackIndex: number
  typeIndex?: number
}

const IconByTrackType: Partial<Record<TrackType, FC>> = {
  [TrackType.VIDEO]: Video,
  [TrackType.SOUND]: Music,
  [TrackType.TEXT]: Type,
  [TrackType.IMAGE]: Image,
  [TrackType.MIXED]: Layers,
  [TrackType.NARRATION]: Mic,
  [TrackType.STORYBOARD]: Camera,
}

export const TimelineTrackHeader: FC<TimelineTrackHeaderProps> = ({
  trackIndex, type, typeIndex, isGlobalTrack
}) => {
  const draggable = type !== TrackType.STORYBOARD

  const {
    dragOverRowIndex,
    draggedRowIndex,
    timelineTrackDndHandlers: {
      onRowDragStart, onRowDrop, onRowDragEnd, onRowDragOver
    }
  } = useTimeline()

  const { moveTrack } = useOverlayHelper()

  const isDragging = useMemo(() => draggedRowIndex === trackIndex, [draggedRowIndex])
  const isDragOver = useMemo(() => dragOverRowIndex === trackIndex, [dragOverRowIndex])

  // Add visual feedback state
  const [isDraggingRow, setIsDraggingRow] = useState(false)

  const handleRowDragStart = () => {
    setIsDraggingRow(true)
    onRowDragStart(trackIndex)
  }

  const handleRowDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    onRowDragOver(trackIndex)
  }

  const handleRowDrop = useCallback((targetIndex: number) => {
    if (draggedRowIndex === null) return

    moveTrack(draggedRowIndex, targetIndex)
    setIsDraggingRow(false)
    onRowDrop()
  }, [draggedRowIndex])

  const handleRowDragEnd = () => {
    setIsDraggingRow(false)
    onRowDragEnd()
  }

  const trackLabel = useMemo(() => {
    if (type === TrackType.STORYBOARD) return '分镜轨道'

    return `${isGlobalTrack ? '全局' : '分镜'}${getTrackTypeLabel(type)}轨道${typeof typeIndex === 'number' ? `-${typeIndex}` : ''}`
  }, [isGlobalTrack, type, typeIndex])

  return (
    <>
      <div
        className={clsx(
          'flex items-center justify-center transition-all duration-200 border-2',
          isDragging && 'opacity-50 bg-gray-100/50 dark:bg-gray-800/50',
          isDraggingRow && 'cursor-grabbing',
          draggable ? 'hover:bg-gray-100 dark:hover:bg-gray-800/30' : 'cursor-not-allowed',
          isDragOver
            ? 'bg-blue-50 dark:bg-blue-900/20 border-dashed border-blue-300 dark:border-blue-500'
            : 'border-transparent',
        )}
        onDragOver={handleRowDragOver}
        onDrop={() => handleRowDrop(trackIndex)}
      >
        {ENABLE_TRACK_DRAG && (
          <div
            className={clsx(
              `flex items-center justify-center rounded-md
          transition-all duration-150
          active:scale-95
          group`,
              draggable && 'cursor-grab hover:bg-gray-200 dark:hover:bg-gray-700',
              isDraggingRow && 'cursor-grabbing',
            )}
            draggable={draggable}
            onDragStart={handleRowDragStart}
            onDragEnd={handleRowDragEnd}
          >
            <Grip
              className={clsx(
                'w-3 h-3 text-gray-400 dark:text-gray-500 transition-colors duration-150',
                draggable && 'group-hover:text-gray-600 dark:group-hover:text-gray-300'
              )}
            />
          </div>
        )}
      </div>

      {type && IconByTrackType[type] && (
        <WithTooltip content={trackLabel}>
          <div className="size-5 flex justify-center items-center gap-px border rounded-xs">
            {/* @ts-ignore*/}
            {React.createElement(IconByTrackType[type], { className: clsx('text-gray-500', typeof typeIndex === 'number' ? 'size-[10px]' : 'size-3') })}
            {typeof typeIndex === 'number' && (
              <div className="text-[9px] font-thin text-gray-500">{typeIndex}</div>
            )}
          </div>
        </WithTooltip>
      )}
    </>
  )
}
