import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { useCurrentScale } from 'remotion'
import { useEditorContext } from '@rve/editor/contexts'
import { Overlay, OverlayType, RenderableOverlay } from '@app/rve-shared/types'
import { DndContext, DragMoveEvent, PointerSensor, useDraggable, useSensor, useSensors } from '@dnd-kit/core'
import { clamp, isEqual, omit } from 'lodash'
import { getOverlayTimeRange } from '@app/rve-shared/utils'
import { ScaleHandle } from './scale-handle'
import { RotateHandle } from './rotate-handle'
import { ResizeHandle } from './resize-handle'
import { useCachedOverlaysContext } from '@rve/editor/contexts/cached-overlays.context.tsx'
import { LAYER_CONTROL_DRAG_ACTIONS } from '@rve/editor/components/core/layer-control/constants.ts'
import { useQuery } from '@tanstack/react-query'
import { cacheManager } from '@/libs/cache/cache-manager.ts'
import { useCalculateTextRenderInfo } from '@rve/renderer/hooks/useCalculateTextRenderInfo.ts'

type LayerControlProps = {
  overlay: Overlay
}

function useStateToRef<T>(state: T) {
  const ref = useRef<T>(state)

  useEffect(() => {
    ref.current = state
  }, [state])

  return ref
}

export type SelectionOutlineProps = {
  /**
   * The overlay object containing position, size, and other properties
   */
  overlay: RenderableOverlay
}

/**
 * SelectionOutline is a component that renders a draggable, resizable outline around selected overlays.
 * It provides visual feedback and interaction handles for manipulating overlay elements.
 *
 * @component
 */
const SelectionOutline: React.FC<SelectionOutlineProps> = ({ overlay }) => {
  const scale = useCurrentScale()
  const scaledBorder = Math.ceil(1 / scale)

  const {
    selectedOverlay,
    videoPlayer: { currentFrame },
    setSelectedOverlay
  } = useEditorContext()

  const ref = useRef<HTMLDivElement | null>(null)

  const { setNodeRef, listeners, attributes, isDragging } = useDraggable({
    id: `later-outline-${overlay.id}`,
    data: {
      action: LAYER_CONTROL_DRAG_ACTIONS.move,
      overlay
    }
  })

  const [hovered, setHovered] = React.useState(false)

  const enableToBeSelected = useMemo(() => {
    if (currentFrame === undefined) return false
    const [from, end] = getOverlayTimeRange(overlay)
    return from <= currentFrame && currentFrame < end
  }, [currentFrame, overlay])

  const onMouseEnter = useCallback(() => {
    if (enableToBeSelected) {
      setHovered(true)
    }
  }, [overlay, enableToBeSelected])

  const onMouseLeave = useCallback(() => {
    setHovered(false)
  }, [])

  const isSelected = useMemo(
    () => overlay.id === selectedOverlay?.id,
    [overlay.id, selectedOverlay]
  )

  const style: React.CSSProperties = useMemo(
    () => ({
      width: Number.isFinite(overlay.width) ? overlay.width : 0,
      height: Number.isFinite(overlay.height) ? overlay.height : 0,
      left: overlay.left,
      top: overlay.top,
      position: 'absolute',
      outline: (hovered && !isDragging) || isSelected
        ? `${scaledBorder}px solid #3B8BF2`
        : undefined,
      transform: `rotate(${overlay.rotation || 0}deg)`,
      transformOrigin: 'center center',
      userSelect: 'none',
      touchAction: 'none',
      zIndex: overlay.zIndex,
      pointerEvents: enableToBeSelected ? 'all' : 'none',
      cursor: 'pointer'
    }),
    [overlay, isDragging, hovered, isSelected, scaledBorder, enableToBeSelected]
  )

  if (overlay.type === OverlayType.SOUND) {
    return null
  }

  return (
    <>
      <div
        ref={el => {
          ref.current = el
          setNodeRef(el)
        }}
        {...listeners}
        {...attributes}
        onClick={() => setSelectedOverlay(overlay)}
        onPointerEnter={onMouseEnter}
        onPointerLeave={onMouseLeave}
        style={style}
      >
        {isSelected
          ? (
            <>
              <ScaleHandle
                overlay={overlay}
                type="top-left"
              />
              <ScaleHandle
                overlay={overlay}
                type="top-right"
              />
              <ScaleHandle
                overlay={overlay}
                type="bottom-left"
              />
              <ScaleHandle
                overlay={overlay}
                type="bottom-right"
              />
              <RotateHandle
                overlay={overlay}
                outlineRef={ref}
              />
              <ResizeHandle
                overlay={overlay}
                type="center-right"
              />
            </>
          )
          : null}
      </div>
    </>
  )
}

export const LayerControl: React.FC<LayerControlProps> = ({
  overlay
}) => {
  const scale = useCurrentScale()

  const { requestUpdate } = useCachedOverlaysContext()
  const { setSelectedOverlay, getAspectRatioDimensions } = useEditorContext()
  const { width: playerWidth, height: playerHeight } = getAspectRatioDimensions()

  const localOverlayRef = useStateToRef(overlay)
  const initialOverlayRef = useRef<Overlay | null>(null)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1
      }
    })
  )

  const handleDragStart = useCallback(() => {
    setSelectedOverlay?.(overlay)
    requestUpdate(overlay.id, prev => {
      initialOverlayRef.current = prev
      return {
        ...prev,
        isDragging: true
      }
    })
  }, [overlay, requestUpdate])

  const { data: font = null } = useQuery({
    queryKey: ['LAYER_CONTROL_FONT', 'src' in overlay && overlay.src],
    queryFn: () => {
      if (!('src' in overlay)) return null

      return cacheManager.font.getFont(overlay.src)
    },
    enabled: overlay.type === OverlayType.TEXT && 'src' in overlay,
    refetchInterval: query => {
      if (query.state.data) return false
      return 1000
    }
  })

  const { buildCalcTextRenderInfoFunction } = useCalculateTextRenderInfo(
    font,
    overlay.type === OverlayType.TEXT ? overlay : null,
  )

  const handleResize = useCallback(
    (initial: Overlay, deltaX: number) => {
      const newWidth = initial.width + deltaX

      const calc = buildCalcTextRenderInfoFunction()
      const maybeRenderInfo = calc?.({ width: newWidth })

      const constrainedWidth = clamp(
        Math.round(newWidth),
        maybeRenderInfo?.minWidth || 1,
        playerWidth
      )

      const constrainedLeft = clamp(Math.round(initial.left), 0, playerWidth - constrainedWidth)

      requestUpdate(overlay.id, {
        left: constrainedLeft,
        width: constrainedWidth,
        ...(maybeRenderInfo && { height: maybeRenderInfo.minHeight })
      })
    },
    [buildCalcTextRenderInfoFunction]
  )

  const handleDragMove = useCallback(
    (event: DragMoveEvent) => {
      const initial = initialOverlayRef.current
      if (!initial) return

      const { action, type, outlineRef } = event.active.data.current || {}

      const { clientX: initialClientX, clientY: initialClientY } = event.activatorEvent as PointerEvent
      const deltaX = event.delta.x / scale
      const deltaY = event.delta.y / scale

      if (action === LAYER_CONTROL_DRAG_ACTIONS.move) {
        requestUpdate(overlay.id, {
          left: initial.left + deltaX,
          top: initial.top + deltaY
        })
      }
      else if (action === LAYER_CONTROL_DRAG_ACTIONS.scale) {
        // 提取公共的位置判断逻辑
        const isLeft = type === 'top-left' || type === 'bottom-left'
        const isTop = type === 'top-left' || type === 'top-right'

        let newWidth: number
        let newHeight: number
        let newLeft: number
        let newTop: number

        if (overlay.type !== OverlayType.TEXT) {
          // 非文字类型：自由缩放
          newWidth = initial.width + (isLeft ? -deltaX : deltaX)
          newHeight = initial.height + (isTop ? -deltaY : deltaY)
          newLeft = initial.left + (isLeft ? deltaX : 0)
          newTop = initial.top + (isTop ? deltaY : 0)
        } else {
          // 文字类型：等比缩放
          const scaleFactorX = Math.abs(deltaX) / initial.width
          const scaleFactorY = Math.abs(deltaY) / initial.height
          const scaleFactor = Math.max(scaleFactorX, scaleFactorY)

          const isScalingUp = (isLeft ? -deltaX : deltaX) > 0 || (isTop ? -deltaY : deltaY) > 0
          let finalScaleFactor = isScalingUp ? (1 + scaleFactor) : (1 - scaleFactor)

          // 计算预期的新尺寸
          let proposedWidth = initial.width * finalScaleFactor
          let proposedHeight = initial.height * finalScaleFactor

          // 检查边界限制，如果超出边界则限制缩放因子
          const maxAllowedWidth = playerWidth - initial.left
          const maxAllowedHeight = playerHeight - initial.top

          if (proposedWidth > maxAllowedWidth) {
            finalScaleFactor = maxAllowedWidth / initial.width
            proposedWidth = maxAllowedWidth
            proposedHeight = initial.height * finalScaleFactor
          }

          if (proposedHeight > maxAllowedHeight) {
            finalScaleFactor = Math.min(finalScaleFactor, maxAllowedHeight / initial.height)
            proposedWidth = initial.width * finalScaleFactor
            proposedHeight = initial.height * finalScaleFactor
          }

          // 确保最小尺寸
          if (proposedWidth < 1 || proposedHeight < 1) {
            finalScaleFactor = Math.max(1 / initial.width, 1 / initial.height)
            proposedWidth = initial.width * finalScaleFactor
            proposedHeight = initial.height * finalScaleFactor
          }

          newWidth = proposedWidth
          newHeight = proposedHeight
          newLeft = initial.left + (isLeft ? (initial.width - newWidth) : 0)
          newTop = initial.top + (isTop ? (initial.height - newHeight) : 0)
        }

        // 公共的边界约束逻辑
        const constrainedWidth = clamp(Math.round(newWidth), 1, playerWidth)
        const constrainedHeight = clamp(Math.round(newHeight), 1, playerHeight)
        const constrainedLeft = clamp(Math.round(newLeft), 0, playerWidth - constrainedWidth)
        const constrainedTop = clamp(Math.round(newTop), 0, playerHeight - constrainedHeight)

        // 公共的状态更新逻辑
        requestUpdate(overlay.id, {
          left: constrainedLeft,
          top: constrainedTop,
          width: constrainedWidth,
          height: constrainedHeight
        })
      }
      else if (action === LAYER_CONTROL_DRAG_ACTIONS.resize) {
        handleResize(initial, deltaX)
      }
      else if (action === LAYER_CONTROL_DRAG_ACTIONS.rotate) {
        const rect = outlineRef.current.getBoundingClientRect()

        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        const getAngle = (x: number, y: number) => {
          const deltaX = x - centerX
          const deltaY = y - centerY
          return Math.atan2(deltaY, deltaX) * (180 / Math.PI) + 90
        }

        const currentAngle = getAngle(
          initialClientX + event.delta.x,
          initialClientY + event.delta.y
        )

        requestUpdate(overlay.id, {
          rotation: currentAngle
        })
      }
    },
    [scale, requestUpdate, handleResize]
  )

  const handleDragEnd = useCallback(() => {
    const initial = initialOverlayRef.current
    const current = localOverlayRef.current
    if (!isEqual(omit(current, 'isDragging'), omit(initial, 'isDragging'))) {
      requestUpdate?.(overlay.id, current, true)
    }
  }, [requestUpdate])

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
    >
      <SelectionOutline overlay={overlay as any} />
    </DndContext>
  )
}
