import React, { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react'

import { useProjectSaver } from './useProjectSaver.tsx'
import { HistoryHook, useHistory } from './useHistory'
import { OverlaysHook, useOverlays } from './useOverlays'
import { useVideoPlayer, VideoPlayerHook } from './useVideoPlayer'
import { AspectRatioHook, useAspectRatio } from './useAspectRatio'
import { CompositionDuration, useCompositionDuration } from './useCompositionDuration'

import { cleanupPlugins, initializePlugins } from '@/modules/video-editor/plugins'
import { loadProjectState } from '@rve/editor/utils/project-state-storage.ts'
import { CloudLoadErrorDialog } from '../../components/cloud-load-error-dialog.tsx'
import { PageLockedMask } from '@rve/editor/components/page-locked-mask.tsx'

export interface EditorContextValues extends OverlaysHook,
  AspectRatioHook,
  Pick<CompositionDuration, 'durationInFrames' | 'durationInSeconds'>
{
  scriptId: string
  projectId: string

  videoPlayer: VideoPlayerHook
  history: HistoryHook

  /**
   * Manual save project
   */
  saveProject(): Promise<void>

  /**
   * Trigger media rendering
   * @deprecated
   */
  renderMedia: () => void

  /**
   * General state object with proper typing
   * @deprecated
   */
  state: any
}

const EditorContext = createContext<EditorContextValues | undefined>(undefined)

export const EditorProvider: React.FC<{
  scriptId: string
  projectId: string
  children: ReactNode
}> = ({ children, scriptId, projectId }) => {
  // 云端加载状态管理
  const [cloudLoadState, setCloudLoadState] = useState<{
    isLoading: boolean
    hasError: boolean
    error?: string
    showErrorDialog: boolean
  }>({
    isLoading: false,
    hasError: false,
    showErrorDialog: false
  })

  const overlaysHook = useOverlays()

  const videoPlayerHook = useVideoPlayer()

  const aspectRatioHook = useAspectRatio()

  const { tracks, setTracksDirectly } = overlaysHook

  const compositionDuration = useCompositionDuration(tracks)

  const historyHook = useHistory(tracks, setTracksDirectly)

  const { saveProject, ProjectSaver } = useProjectSaver(
    scriptId,
    true,
    {
      tracks,
      aspectRatio: aspectRatioHook.aspectRatio,
      playerDimensions: aspectRatioHook.getAspectRatioDimensions(),
      playerMetadata: {
        ...aspectRatioHook.getAspectRatioDimensions(),
        fps: videoPlayerHook.fps,
        durationInFrames: compositionDuration.durationInFrames
      }
    },
    {
      onLoad: loadedState => {
        if (loadedState) {
          const { tracks, aspectRatio, playerDimensions } = loadedState
          setTracksDirectly(tracks || [])

          if (aspectRatio) aspectRatioHook.setAspectRatio(aspectRatio)
          if (playerDimensions) {
            aspectRatioHook.updatePlayerDimensions(
              playerDimensions.width,
              playerDimensions.height
            )
          }
        }
      },
      onCloudLoadStart: useCallback(() => {
        setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))
      }, []),
      onCloudLoadSuccess: useCallback(() => {
        setCloudLoadState(prev => ({ ...prev, isLoading: false }))
      }, []),
      onCloudLoadError: useCallback((error: Error) => {
        console.error('[EditorProvider] 云端状态加载失败:', error)

        if (error.message === 'CLOUD_LOAD_FAILED') {
          // 云端加载失败，显示错误对话框并锁定编辑器
          setCloudLoadState({
            isLoading: false,
            hasError: true,
            error: '网络连接失败或服务器错误',
            showErrorDialog: true
          })
        } else {
          // 其他错误，静默处理
          setCloudLoadState(prev => ({ ...prev, isLoading: false }))
        }
      }, [])
    }
  )

  useEffect(() => {
    initializePlugins()

    return () => {
      cleanupPlugins()
    }
  }, [])

  // 错误处理函数
  const handleRetryCloudLoad = () => {
    setCloudLoadState(prev => ({ ...prev, showErrorDialog: false }))
    // 重新触发加载
    const loadCloudState = async () => {
      try {
        setCloudLoadState(prev => ({ ...prev, isLoading: true, hasError: false }))

        const cloudState = await loadProjectState(scriptId, true)

        if (cloudState?.tracks) {
          overlaysHook.updateTracks(cloudState.tracks)
          console.log('[EditorProvider] 云端状态重新加载成功')
        }

        setCloudLoadState(prev => ({ ...prev, isLoading: false }))
      } catch (error) {
        console.error('[EditorProvider] 云端状态重新加载失败:', error)
        setCloudLoadState({
          isLoading: false,
          hasError: true,
          error: '网络连接失败或服务器错误',
          showErrorDialog: true
        })
      }
    }

    void loadCloudState()
  }

  const handleCloseEditor = () => {
    // 关闭编辑器标签页或返回上一页
    window.history.back()
  }

  return (
    <EditorContext.Provider
      value={{
        scriptId,
        projectId,

        ...overlaysHook,
        ...aspectRatioHook,
        ...historyHook,
        ...compositionDuration,
        videoPlayer: videoPlayerHook,
        history: historyHook,

        renderMedia: () => { },
        state: {},
        saveProject,
      }}
    >
      {children}

      <ProjectSaver />

      {/* 云端加载错误对话框 */}
      <CloudLoadErrorDialog
        open={cloudLoadState.showErrorDialog}
        onRetry={handleRetryCloudLoad}
        onClose={handleCloseEditor}
        error={cloudLoadState.error}
      />

      {/* 编辑器锁定遮罩 */}
      <PageLockedMask
        isLoading={cloudLoadState.isLoading}
        hasError={cloudLoadState.hasError && !cloudLoadState.showErrorDialog}
        message={cloudLoadState.error}
      />
    </EditorContext.Provider>
  )
}

export const useEditorContext = (): EditorContextValues => {
  const context = useContext(EditorContext)
  if (!context) {
    // 在开发环境中，如果是热重载导致的上下文丢失，给出更友好的错误信息
    if (import.meta.env.DEV) {
      console.warn('EditorContext 不可用，可能是热重载导致的。请等待组件重新初始化。')
      // 在开发环境中，我们可以抛出一个特殊的错误，让 React 的错误边界捕获
      throw new Error('EDITOR_CONTEXT_HOT_RELOAD')
    }
    throw new Error('useEditorContext must be used within an EditorProvider')
  }
  return context
}
