import { useEditorContext } from '@rve/editor/contexts/editor/editor.context.tsx'
import { useCallback, useMemo, useState } from 'react'
import { TrackType } from '@app/rve-shared/types'
import {
  NARRATOR_TIMELINE_TRACK_HEIGHT,
  NORMAL_TIMELINE_TRACK_HEIGHT,
  TIMELINE_GRID_GAP_PX
} from '@rve/editor/constants'

export type TimelineTracksLayout = {
  layout: Array<{ height: number, top: number }>
  totalHeight: number
  rowGap: number

  getTrackHeight(trackIndex: number): number
}

export const useTimelineTracksLayout = (): TimelineTracksLayout => {
  const { tracks } = useEditorContext()

  const [rowGap] = useState(TIMELINE_GRID_GAP_PX)

  const { layout, totalHeight } = useMemo(() => {
    let totalHeight = 0

    const layout: Array<{ height: number, top: number }> = []

    for (const track of tracks) {
      const height = track.type === TrackType.NARRATION
        ? NARRATOR_TIMELINE_TRACK_HEIGHT
        : NORMAL_TIMELINE_TRACK_HEIGHT

      layout.push({
        height,
        top: totalHeight
      })
      totalHeight += height + rowGap
    }

    return { layout, totalHeight }
  }, [tracks, rowGap])

  const getTrackHeight = useCallback<TimelineTracksLayout['getTrackHeight']>(
    trackIndex => {
      return layout[trackIndex]?.height || NORMAL_TIMELINE_TRACK_HEIGHT
    },
    [layout]
  )

  return {
    layout,
    rowGap,
    totalHeight,
    getTrackHeight,
  }
}
