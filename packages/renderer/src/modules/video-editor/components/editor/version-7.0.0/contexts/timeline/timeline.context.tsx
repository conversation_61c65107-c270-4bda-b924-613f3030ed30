import React, { createContext, useContext, useRef, useState } from 'react'

import { useEditorContext } from '@rve/editor/contexts/editor.context'

import { TimelineZoomHook, useTimelineZoom } from './useTimelineZoom'
import { TimelineTrackDnDHook, useTimelineTrackDnD } from './useTimelineTrackDnD'
import { TimelineSnappingHook, useTimelineSnapping } from './useTimelineSnapping'
import { TimelineClipboard, useTimelineClipboard } from './useTimelineClipboard'
import { TimelineOverlayDnDHook, useTimelineOverlayDnD } from './useTimelineOverlayDnD'
import { TimelineOverlayActivation, useTimelineOverlayActivation } from './useTimelineOverlayActivation.ts'

import { SNAPPING_CONFIG } from '@rve/editor/constants'
import { TimelineTracksLayout, useTimelineTracksLayout } from '@rve/editor/contexts/timeline/useTimelineTracksLayout.ts'

/**
 * Context interface for managing timeline state and interactions.
 * @interface TimelineContextType
 */
interface TimelineContextType extends
  TimelineTrackDnDHook,
  TimelineOverlayDnDHook,
  TimelineZoomHook,
  TimelineOverlayActivation,
  Omit<TimelineSnappingHook, 'snappedLandingPoint'>
{
  layout: TimelineTracksLayout
  clipboard: TimelineClipboard

  isContextMenuOpen: boolean,
  setIsContextMenuOpen(v: boolean): void

  /** Reference to the timeline grid DOM element */
  timelineGridRef: React.RefObject<HTMLDivElement | null>

  /**
   *  Number of currently visible rows in the timeline
   *  @deprecated use `useEditorContext().tracks.length` instead
   */
  visibleRows: number
}

/**
 * Context for sharing timeline state and functionality across components.
 */
export const TimelineContext = createContext<TimelineContextType>({} as any)

/**
 * Provider component that manages timeline state and makes it available to child components.
 * Combines functionality from multiple hooks to handle timeline interactions.
 *
 * @component
 */
export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  // State for context menu visibility
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)

  const timelineGridRef = useRef<HTMLDivElement>(null)

  const clipboard = useTimelineClipboard()
  const zoom = useTimelineZoom(timelineGridRef)
  const trackDragAndDrop = useTimelineTrackDnD()
  const overlaySelection = useTimelineOverlayActivation()

  const layout = useTimelineTracksLayout()

  const { landingPoint, ...timelineState } = useTimelineOverlayDnD(
    timelineGridRef,
    zoom.zoomScale,
    isContextMenuOpen
  )

  const { snappedLandingPoint, alignmentLines } = useTimelineSnapping({
    landingPoint,
    isDragging: timelineState.isDragging,
    draggingOverlay: timelineState.draggingOverlay,
    dragInfo: timelineState.dragInfo,
    snapThreshold: SNAPPING_CONFIG.thresholdFrames,
  })

  return (
    <TimelineContext.Provider
      value={{
        ...trackDragAndDrop,
        ...zoom,
        ...timelineState,
        ...overlaySelection,
        clipboard,
        layout,

        isContextMenuOpen,
        setIsContextMenuOpen,

        alignmentLines,
        landingPoint: snappedLandingPoint,
        visibleRows: useEditorContext().tracks.length,
        timelineGridRef,
      }}
    >
      {children}
    </TimelineContext.Provider>
  )
}

export const useTimeline = () => {
  const context = useContext(TimelineContext)
  if (!context) {
    throw new Error('useTimeline must be used within a TimelineProvider')
  }
  return context
}
