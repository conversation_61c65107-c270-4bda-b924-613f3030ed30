import React from 'react'
import {
  Overlay,
  OverlayType
} from '@app/rve-shared/types'
import { GetTypedOverlay, OverlayUpdater } from './cached-overlays.context'

export type OverlayEditingContextValues = {
  localOverlay: Overlay,
  requestUpdate<TOverlayType extends OverlayType>(
    overlay: OverlayUpdater<GetTypedOverlay<TOverlayType>>,
    commit?: boolean
  ): void
}

export const OverlayEditingContext = React.createContext<OverlayEditingContextValues>(null as any)

export function useOverlayEditing<TOverlay extends Overlay>() {
  const context = React.useContext(OverlayEditingContext) as OverlayEditingContextValues | null
  if (!context) {
    throw new Error('useOverlaySetting must be used within an OverlaySettingContext')
  }
  const { localOverlay, requestUpdate } = context
  return {
    localOverlay: localOverlay as TOverlay,
    requestUpdate
  }
}
