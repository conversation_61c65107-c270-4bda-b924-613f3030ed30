import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Plus,
  Edit2,
  ArrowLeft,
  Share2,
  Video,
  ChevronRight,
  CircleX,
  Check,
  Loader2,
  Save,
  ShieldCheck,
  ShieldAlert,
  Trash,
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'
import { SelectTrigger } from '@radix-ui/react-select'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import Editor from 'react-simple-code-editor'
import './script.css'
import { Link, useParams } from 'react-router'
import { cn } from '@/components/lib/utils'
import { uploadFileToOss } from '@/libs/request/upload'
import { useQueryScript } from '@/hooks/queries/useQueryScript'
import { PageLoading } from '@/components/PageLoading'
import { ResourceModule } from '@/libs/request/api/resource'
import { debounce, isEqual } from 'lodash'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ComplianceAPI, RiskKeyword } from '@/libs/request/api/rsooio'
import useVirtualTabsStore from '@/stores/useVirtualTabsStore.ts'

interface SceneData {
  id: string
  title?: string
  shotType?: string
  script?: string
  notes?: string
  refImg?: string
}

const shotTypes = [
  { value: 'long', label: '远景' },
  { value: 'medium', label: '中景' },
  { value: 'close', label: '近景' },
  { value: 'closeup', label: '特写' },
  { value: 'full', label: '全景' },
]

function useAutoSave(callback: () => void | Promise<void>, deps: unknown[] = []) {
  const [isDirty, setIsDirty] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const callbackRef = useRef(callback)

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  const debounced = useMemo(
    () =>
      debounce(async () => {
        setIsSaving(true)
        try {
          await callbackRef.current()
          setLastSaved(new Date())
          setIsDirty(false)
        } catch (error) {
          console.error('Error saving:', error)
        } finally {
          setIsSaving(false)
        }
      }, 1000),
    [],
  )

  useEffect(() => {
    setIsDirty(true)
    debounced()
  }, [...deps, debounced])

  return { isDirty, isSaving, lastSaved, saveNow: debounced.flush }
}

function ScriptEditor({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  const [keywords, setKeywords] = useState<RiskKeyword[]>([])
  const { isDirty, isSaving } = useAutoSave(async () => {
    const keywords = await ComplianceAPI.checkRiskKeywords({ text: value })
    setKeywords(keywords)
  }, [value])

  return (
    <>
      <Editor
        className="h-full w-full editor"
        value={value}
        onValueChange={onChange}
        padding={10}
        highlight={text => {
          return text
            .split('\n')
            .map(
              (line, i) =>
                `<span class="editorLineNumber">${i + 1}.</span>${line || '<span class="editorPlaceholder">请输入或添加台词</span>'}`,
            )
            .join('\n')
        }}
      />

      {isSaving && (
        <div className="absolute left-2 bottom-2 bg-blue-50 rounded p-1 pl-5 flex gap-1 items-center">
          <Loader2 className="absolute left-1 size-3.5 text-blue-400 animate-spin" />
          <span className="text-xs font-medium leading-none text-blue-600">风险词检测中...</span>
        </div>
      )}
      {!isDirty && !keywords.length && (
        <div className="absolute left-2 bottom-2 bg-green-50 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldCheck className="absolute left-[3px] size-4 fill-green-400 text-green-100" />
          <span className="text-xs font-medium leading-none text-green-600">未检测到风险词</span>
        </div>
      )}
      {!isDirty && !!keywords.length && (
        <div className="absolute left-2 bottom-2 bg-red-50 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldAlert className="absolute left-[3px] size-4 fill-red-400 text-red-100" />
          <span className="text-xs font-medium leading-none text-red-600">检测到 1 个风险词</span>
        </div>
      )}
    </>
  )
}

export default function StoryboardTable() {
  const { pushTab } = useVirtualTabsStore()
  const queryClient = useQueryClient()
  const { creativeId, projectId } = useParams()

  const { data } = useQueryScript(creativeId!)

  const [scenes, setScenes] = useState<SceneData[] | undefined>(undefined)
  const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({})
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [projectTitle, setProjectTitle] = useState('分镜脚本')

  const { isDirty, isSaving, lastSaved, saveNow } = useAutoSave(async () => {
    if (isEqual(scenes, data?.content)) return

    await ResourceModule.script.update({
      id: Number(creativeId!),
      content: JSON.stringify(scenes),
      fullContent: '[]',
    })
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_DETAIL, creativeId] })
  }, [scenes])

  useEffect(() => {
    if (!data?.content || scenes) return
    setScenes(data.content)
  }, [data, scenes])

  const updateScene = (id: string, field: keyof SceneData, value: string) => {
    if (!scenes) return
    setScenes(scenes.map(scene => (scene.id === id ? { ...scene, [field]: value } : scene)))
  }

  const newScene = (): SceneData => {
    return { id: crypto.randomUUID() }
  }

  const addNewScene = () => {
    if (!scenes) return
    setScenes([...scenes, newScene()])
  }

  const handleSelectOpenChange = (sceneId: string, open: boolean) => {
    setOpenSelects(prev => ({ ...prev, [sceneId]: open }))
  }

  const handleTitleEdit = () => {
    setIsEditingTitle(true)
  }

  const handleTitleBlur = async () => {
    setIsEditingTitle(false)
    await ResourceModule.script.rename({ id: Number(creativeId!), title: projectTitle })
  }

  const SmallUploadArea = ({ src }: { src?: string }) => (
    <div
      className="border border-dashed rounded p-2 text-center text-muted-foreground hover:text-primary hover:border-primary
        transition-colors cursor-pointer size-24 flex flex-col justify-center items-center relative"
    >
      {src ? (
        <img src={src} alt="参考画面" className="w-full h-full object-cover" />
      ) : (
        <>
          <Plus className="w-4 h-4 mx-auto mb-1" />
          <div className="text-xs" onClick={async () => {}}>
            上传参考画面
          </div>
        </>
      )}
      <input
        className="absolute inset-0 opacity-0 cursor-pointer"
        type="file"
        onChange={async e => {
          const [file] = e.target.files ?? []
          if (file) {
            await uploadFileToOss(file, projectId!)
          }
        }}
      />
    </div>
  )

  return (
    <div className="w-full max-w-7xl mx-auto p-4 flex h-full flex-col gap-4 overflow-hidden">
      {/* Header */}
      <div className="flex items-center h-10 border-gray-200 gap-3 text-nowrap">
        {isEditingTitle ? (
          <>
            <Input
              value={projectTitle}
              onChange={e => setProjectTitle(e.target.value)}
              onBlur={handleTitleBlur}
              autoFocus
              className="text-xl font-semibold border-0 shadow-none p-0 h-auto bg-transparent focus-visible:ring-0 outline-none"
              style={{ boxShadow: 'none', outline: 'none' }}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTitleEdit}
              className="p-1 h-auto hover:bg-gray-100"
            >
              <Check className="w-4 h-4 text-gray-500" />
            </Button>
          </>
        ) : (
          <>
            <span className="text-xl font-semibold text-gray-900">{projectTitle}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTitleEdit}
              className="p-1 h-auto hover:bg-gray-100"
            >
              <Edit2 className="w-4 h-4 text-gray-500" />
            </Button>
          </>
        )}
        {scenes && (
          <div className="text-sm text-gray-500 flex items-center gap-1">
            {isSaving ? (
              <>
                <span>保存中</span>
                <Loader2 className="size-4 animate-spin" />
              </>
            ) : isDirty ? (
              <>
                <span>已编辑</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={saveNow}
                  className="size-6 h-auto hover:bg-gray-100"
                >
                  <Save className="size-4" />
                </Button>
              </>
            ) : (
              `已保存 ${new Date(lastSaved!).toLocaleTimeString('zh-CN')}`
            )}
          </div>
        )}
        <Button
          variant="secondary"
          size="sm"
          className={cn('flex items-center gap-2', isEditingTitle && 'hidden')}
          asChild
        >
          <Link to="../.." relative="path">
            <ArrowLeft className="w-4 h-4" />
            返回上一级
          </Link>
        </Button>
        <Button
          variant="secondary"
          size="sm"
          className={cn('flex items-center gap-2', isEditingTitle || 'ml-auto')}
        >
          <Share2 className="w-4 h-4" />
          分享脚本
        </Button>
        <Button
          size="sm"
          className="flex items-center gap-2"
          onClick={() => pushTab({ componentKey: 'Editor', params: { scriptId: creativeId } })}
        >
          <Video className="w-4 h-4" />
          前往混剪
        </Button>
      </div>

      <Card className="border border-gray-200 shadow-sm flex-1 min-h-0 *:h-full *:rounded-xl">
        <Table className="table-fixed border-b">
          <TableHeader className="sticky top-0 rounded-sm bg-muted z-10">
            <TableRow className="rounded-lg *:border-r *:border-dashed">
              <TableHead className="w-32 font-medium text-gray-700 border-dashed">
                分镜编号
              </TableHead>
              <TableHead className="w-32 font-medium text-gray-700 border-dashed">景别</TableHead>
              <TableHead className="font-medium text-gray-700 border-dashed">
                <div className="flex items-center justify-between">
                  <span>话术</span>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span>中文+简体</span>
                    <span className="text-blue-500 cursor-pointer hover:underline">翻译</span>
                  </div>
                </div>
              </TableHead>
              <TableHead className="w-80 font-medium text-gray-700">参考画面要求/备注</TableHead>
            </TableRow>
          </TableHeader>
          {scenes?.length ? (
            <TableBody className="overflow-auto">
              {scenes.map((scene, index) => (
                <TableRow key={scene.id} className="h-60 *:border-r *:border-dashed">
                  <TableCell className="group">
                    <div className="relative">
                      <div className="text-base mb-2">分镜 {index + 1}</div>
                      <Input
                        placeholder="点击输入分镜名称"
                        value={scene.title}
                        onChange={e => updateScene(scene.id, 'title', e.target.value)}
                        className="rounded-none border-0 shadow-none p-0 h-auto text-sm text-gray-500 placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                        style={{ boxShadow: 'none', outline: 'none' }}
                      />
                      <Button
                        variant="secondary"
                        size="icon"
                        className="size-5 rounded absolute left-0 top-1/2 -translate-y-3/2 bg-black/30 hover:bg-black/40 hidden group-hover:flex"
                        onClick={() => {
                          return setScenes(
                            scenes.flatMap(s => (s.id === scene.id ? [s, newScene()] : [s])),
                          )
                        }}
                      >
                        <Plus className="size-4 text-white" />
                      </Button>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="size-5 rounded absolute left-0 top-1/2 translate-y-1/2 bg-black/30 hover:bg-black/40 hidden group-hover:flex"
                        onClick={() => setScenes(scenes.filter(s => s.id !== scene.id))}
                      >
                        <Trash className="size-3.5 text-white" />
                      </Button>
                    </div>
                  </TableCell>

                  <TableCell className="text-center">
                    <div className="group inline-flex items-center gap-1">
                      <Select
                        value={scene.shotType ?? ''}
                        onValueChange={value => updateScene(scene.id, 'shotType', value)}
                        onOpenChange={open => handleSelectOpenChange(scene.id, open)}
                      >
                        <SelectTrigger
                          className="border-0 shadow-none focus:ring-0 focus:ring-offset-0 bg-transparent
                        focus:bg-transparent hover:bg-transparent data-[state=open]:bg-transparent
                        outline-none focus:outline-none p-0 h-auto inline-flex items-center justify-between"
                          style={{ boxShadow: 'none', outline: 'none' }}
                          asChild
                        >
                          <Button variant="secondary">
                            <SelectValue
                              placeholder={<span className="text-muted-foreground">选择场景</span>}
                            />
                            {!scene.shotType && (
                              <ChevronRight
                                className={`w-4 h-4 text-gray-400 transition-transform duration-200 flex-shrink-0 ${
                                  openSelects[scene.id] ? 'rotate-90' : 'rotate-0'
                                }`}
                              />
                            )}
                          </Button>
                        </SelectTrigger>
                        <SelectContent>
                          {shotTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {scene.shotType && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hidden group-hover:inline-block size-6"
                          onClick={() => updateScene(scene.id, 'shotType', '')}
                        >
                          <CircleX className="size-4.5 fill-primary/40 text-background" />
                        </Button>
                      )}
                    </div>
                  </TableCell>

                  <TableCell className="h-60 overflow-hidden p-0 relative">
                    <ScriptEditor
                      value={scene.script ?? ''}
                      onChange={value => updateScene(scene.id, 'script', value)}
                    />
                  </TableCell>

                  <TableCell className="align-top h-60">
                    <div className="flex flex-col h-full">
                      <Textarea
                        placeholder="请输入画面要求，如：从包装盒取出护肤品"
                        value={scene.notes ?? ''}
                        onChange={e => updateScene(scene.id, 'notes', e.target.value)}
                        className="rounded-none resize-none border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                        style={{ boxShadow: 'none', outline: 'none' }}
                      />
                      <div className="flex justify-start mt-auto">
                        <SmallUploadArea />
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <TableCaption>
              {scenes ? (
                <div className="flex flex-col items-center gap-2">
                  <div className="text-sm text-gray-500">暂无分镜</div>
                </div>
              ) : (
                <PageLoading />
              )}
            </TableCaption>
          )}
        </Table>
      </Card>

      <Button variant="secondary" size="lg" disabled={!scenes} onClick={addNewScene}>
        <Plus className="w-4 h-4 mr-2" />
        新增分镜
      </Button>
    </div>
  )
}
